#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للإصلاحات المطبقة
"""

import sys
import os
import numpy as np
import asyncio
import logging

# إضافة مسار src للاستيراد
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# تكوين السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_performance_monitor():
    """اختبار مراقب الأداء المحسن"""
    print("اختبار مراقب الأداء...")
    
    try:
        from monitoring.real_time_performance import RealTimePerformanceMonitor
        
        # إنشاء مثيل من المراقب
        monitor = RealTimePerformanceMonitor()
        
        # اختبار بدء التتبع
        tracking_id = await monitor.start_analysis_tracking("test_user", "BTCUSDT", "traditional")
        print(f"تم بدء التتبع بنجاح: {tracking_id}")

        # اختبار إنهاء التتبع
        await monitor.end_analysis_tracking("test_user", "BTCUSDT", True, 2.5, {"test": True})
        print("تم إنهاء التتبع بنجاح")

        # اختبار الإحصائيات
        stats = monitor.get_performance_stats()
        print(f"الإحصائيات: {stats}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار مراقب الأداء: {str(e)}")
        return False

def test_local_indicators():
    """اختبار المؤشرات المحلية المحسنة"""
    print("اختبار المؤشرات المحلية...")
    
    try:
        from analysis.local_indicators import LocalTechnicalIndicators, _ema_numba, _sma_numba
        
        # إنشاء بيانات اختبار
        test_data = np.array([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
        
        # اختبار EMA
        ema_result = LocalTechnicalIndicators.ema(test_data, 5)
        print(f"EMA: {ema_result[-1]:.2f}")

        # اختبار SMA
        sma_result = LocalTechnicalIndicators.sma(test_data, 5)
        print(f"SMA: {sma_result[-1]:.2f}")

        # اختبار MACD
        macd, signal, histogram = LocalTechnicalIndicators.macd(test_data)
        print(f"MACD: {macd[-1]:.4f}, Signal: {signal[-1]:.4f}")

        # اختبار Bollinger Bands
        upper, middle, lower = LocalTechnicalIndicators.bollinger_bands(test_data, 5)
        print(f"Bollinger: Upper={upper[-1]:.2f}, Middle={middle[-1]:.2f}, Lower={lower[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار المؤشرات المحلية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_optimized_indicators():
    """اختبار المؤشرات المحسنة"""
    print("اختبار المؤشرات المحسنة...")
    
    try:
        from analysis.optimized_indicators import OptimizedIndicators, _simple_moving_average_numba, _fast_ema_calculation_numba
        
        # إنشاء بيانات اختبار
        test_data = np.array([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
        
        # اختبار الدوال المساعدة
        sma_result = _simple_moving_average_numba(test_data, 5)
        print(f"SMA Numba: {sma_result[-1]:.2f}")

        ema_result = _fast_ema_calculation_numba(test_data, 5)
        print(f"EMA Numba: {ema_result[-1]:.2f}")

        # اختبار الكلاس
        optimizer = OptimizedIndicators()
        rsi_result = optimizer._fast_rsi_calculation(test_data, 5)
        print(f"RSI: {rsi_result[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار المؤشرات المحسنة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """الاختبار الرئيسي"""
    print("بدء اختبار الإصلاحات...")
    print("=" * 50)
    
    results = []
    
    # اختبار مراقب الأداء
    results.append(await test_performance_monitor())
    print()
    
    # اختبار المؤشرات المحلية
    results.append(test_local_indicators())
    print()
    
    # اختبار المؤشرات المحسنة
    results.append(test_optimized_indicators())
    print()
    
    # النتائج النهائية
    print("=" * 50)
    print("نتائج الاختبار:")
    print(f"نجح: {sum(results)}/{len(results)} اختبار")

    if all(results):
        print("جميع الاختبارات نجحت! الإصلاحات تعمل بشكل صحيح.")
        return True
    else:
        print("بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
